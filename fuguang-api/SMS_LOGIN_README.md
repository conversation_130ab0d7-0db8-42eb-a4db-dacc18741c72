# 手机验证码登录功能说明

## 功能概述

本项目基于若依框架，新增了手机验证码登录功能，支持用户通过手机号和短信验证码进行登录。短信服务使用阿里云短信服务。

## 功能特性

1. **手机验证码登录**：用户可以使用手机号和短信验证码登录
2. **自动注册**：手机号不存在时自动创建用户账号
3. **频率限制**：防止短信验证码频繁发送（60秒限制）
4. **验证码过期**：验证码5分钟后自动过期
5. **统一登录接口**：支持密码登录和验证码登录两种方式

## 配置说明

### 1. 阿里云短信服务配置

在 `application.yml` 中配置阿里云短信服务参数：

```yaml
# 阿里云短信服务配置
aliyun:
  sms:
    # 访问密钥ID
    access-key-id: ${ALIYUN_ACCESS_KEY_ID:your-access-key-id}
    # 访问密钥Secret
    access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your-access-key-secret}
    # 短信签名
    sign-name: 浮光壁垒
    # 验证码模板代码
    template-code: SMS_123456789
    # 短信发送区域
    region-id: cn-hangzhou
```

### 2. 环境变量配置

建议通过环境变量配置敏感信息：

```bash
export ALIYUN_ACCESS_KEY_ID=your-actual-access-key-id
export ALIYUN_ACCESS_KEY_SECRET=your-actual-access-key-secret
```

## API接口说明

### 1. 发送短信验证码

**接口地址**：`POST /app/sendSmsCode`

**请求参数**：
```json
{
  "phonenumber": "13800138000"
}
```

**响应示例**：
```json
{
  "code": 200,
  "msg": "验证码发送成功",
  "data": null
}
```

### 2. 手机验证码登录

**接口地址**：`POST /app/login`

**请求参数**：
```json
{
  "loginType": "sms",
  "phonenumber": "13800138000",
  "smsCode": "123456"
}
```

**响应示例**：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "user": {
      "userId": 1,
      "userName": "13800138000",
      "nickName": "用户8000",
      "phonenumber": "13800138000",
      "status": "0",
      "userType": "0"
    }
  }
}
```

### 3. 密码登录（原有功能）

**接口地址**：`POST /app/login`

**请求参数**：
```json
{
  "loginType": "password",
  "username": "testuser",
  "password": "123456"
}
```

## 技术实现

### 1. 核心组件

- **SmsService**：短信服务接口，封装阿里云短信发送功能
- **SmsServiceImpl**：短信服务实现类
- **AppLoginService**：登录服务，支持密码和验证码两种登录方式
- **AppLoginController**：登录控制器，提供统一的登录接口
- **PhoneUtils**：手机号工具类，提供手机号验证和验证码生成功能

### 2. 缓存设计

使用Redis缓存验证码和频率限制：

- `sms_code:{phone}`：存储验证码，5分钟过期
- `sms_rate_limit:{phone}`：频率限制标记，60秒过期

### 3. 安全特性

- 手机号格式验证
- 验证码长度和格式验证
- 发送频率限制
- 验证码自动过期
- 用户状态检查

## 编码规范

项目遵循以下编码规范：

1. **命名规范**：
   - 类名使用PascalCase
   - 方法名和变量名使用camelCase
   - 常量名使用UPPER_SNAKE_CASE

2. **注释规范**：
   - 类和方法使用JavaDoc格式注释
   - 包含功能描述、参数说明、返回值说明

3. **代码结构**：
   - Controller层负责接口定义
   - Service层处理业务逻辑
   - 配置类管理配置信息

## 部署说明

1. 确保Redis服务正常运行
2. 配置阿里云短信服务参数
3. 启动应用：`java -jar ruoyi-admin.jar`
4. 访问Swagger文档：`http://localhost:8888/swagger-ui/index.html`

## 注意事项

1. 请确保阿里云短信服务账户余额充足
2. 短信模板需要在阿里云控制台预先配置
3. 生产环境建议使用HTTPS协议
4. 建议配置日志监控短信发送情况

## 错误处理

常见错误及解决方案：

1. **手机号格式不正确**：检查手机号是否为11位数字且以1开头
2. **验证码发送失败**：检查阿里云配置和账户余额
3. **验证码错误或已过期**：重新获取验证码
4. **发送过于频繁**：等待60秒后重试
