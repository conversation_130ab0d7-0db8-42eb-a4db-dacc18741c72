package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.MallCart;
import com.ruoyi.fuguang.service.IMallCartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP购物车接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP购物车接口")
@RestController("appCartController")
@RequestMapping("/app/cart")
public class AppCartController extends BaseController
{
    @Autowired
    private IMallCartService mallCartService;

    /**
     * 获取购物车列表
     */
    @ApiOperation("获取购物车列表")
    @GetMapping("/list")
    public AjaxResult getCartList()
    {
        Long userId = getUserId();
        List<MallCart> list = mallCartService.selectCartListByUserId(userId);
        return success(list);
    }

    /**
     * 添加商品到购物车
     */
    @ApiOperation("添加商品到购物车")
    @PostMapping("/add")
    public AjaxResult addToCart(
            @ApiParam("商品ID") @RequestParam Long productId,
            @ApiParam("数量") @RequestParam(defaultValue = "1") Integer quantity)
    {
        Long userId = getUserId();
        
        if (quantity <= 0) {
            return error("商品数量必须大于0");
        }
        
        int result = mallCartService.addToCart(userId, productId, quantity);
        if (result > 0) {
            return success("添加成功");
        } else {
            return error("添加失败");
        }
    }

    /**
     * 更新购物车商品数量
     */
    @ApiOperation("更新购物车商品数量")
    @PutMapping("/update")
    public AjaxResult updateCartQuantity(
            @ApiParam("商品ID") @RequestParam Long productId,
            @ApiParam("数量") @RequestParam Integer quantity)
    {
        Long userId = getUserId();
        
        if (quantity <= 0) {
            return error("商品数量必须大于0");
        }
        
        int result = mallCartService.updateCartQuantity(userId, productId, quantity);
        if (result > 0) {
            return success("更新成功");
        } else {
            return error("更新失败");
        }
    }

    /**
     * 删除购物车商品
     */
    @ApiOperation("删除购物车商品")
    @DeleteMapping("/remove")
    public AjaxResult removeFromCart(@ApiParam("商品ID") @RequestParam Long productId)
    {
        Long userId = getUserId();
        int result = mallCartService.removeFromCart(userId, productId);
        if (result > 0) {
            return success("删除成功");
        } else {
            return error("删除失败");
        }
    }

    /**
     * 批量删除购物车商品
     */
    @ApiOperation("批量删除购物车商品")
    @DeleteMapping("/remove/batch")
    public AjaxResult removeCartItems(@ApiParam("购物车ID列表") @RequestBody Long[] cartIds)
    {
        Long userId = getUserId();
        int result = mallCartService.removeCartItems(userId, cartIds);
        if (result > 0) {
            return success("删除成功");
        } else {
            return error("删除失败");
        }
    }

    /**
     * 清空购物车
     */
    @ApiOperation("清空购物车")
    @DeleteMapping("/clear")
    public AjaxResult clearCart()
    {
        Long userId = getUserId();
        int result = mallCartService.clearCart(userId);
        if (result > 0) {
            return success("清空成功");
        } else {
            return error("清空失败");
        }
    }

    /**
     * 获取购物车商品数量
     */
    @ApiOperation("获取购物车商品数量")
    @GetMapping("/count")
    public AjaxResult getCartItemCount()
    {
        Long userId = getUserId();
        int count = mallCartService.getCartItemCount(userId);
        
        Map<String, Object> data = new HashMap<>();
        data.put("count", count);
        
        return success(data);
    }
}
