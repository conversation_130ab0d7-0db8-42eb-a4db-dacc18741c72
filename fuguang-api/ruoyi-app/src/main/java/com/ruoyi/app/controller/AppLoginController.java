package com.ruoyi.app.controller;

import com.ruoyi.common.annotation.Anonymous;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginBody;
import com.ruoyi.app.service.AppLoginService;
import com.ruoyi.fuguang.domain.AppUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * APP登录验证
 *
 * <AUTHOR>
 */
@Api(tags = "APP登录接口")
@RestController("appLoginApiController")
@RequestMapping("/app")
public class AppLoginController
{
    @Autowired
    private AppLoginService appLoginService;

    /**
     * APP登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @ApiOperation("APP用户登录")
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        String token;
        AppUser user;

        // 根据登录类型选择不同的登录方式
        if ("sms".equals(loginBody.getLoginType()))
        {
            // 手机验证码登录
            token = appLoginService.smsLogin(loginBody.getPhonenumber(), loginBody.getSmsCode());
            user = appLoginService.getUserInfo(loginBody.getPhonenumber());
        }
        else
        {
            // 密码登录
            token = appLoginService.login(loginBody.getUsername(), loginBody.getPassword());
            user = appLoginService.getUserInfo(loginBody.getUsername());
        }

        if (user != null) {
            user.setPassword(null); // 不返回密码
        }

        AjaxResult ajax = AjaxResult.success();
        ajax.put("token", token);
        ajax.put("user", user);
        return ajax;
    }

    /**
     * APP注册方法
     *
     * @param loginBody 注册信息
     * @return 结果
     */
    @Anonymous
    @ApiOperation("APP用户注册")
    @PostMapping("/register")
    public AjaxResult register(@RequestBody LoginBody loginBody)
    {
        String msg = appLoginService.register(loginBody.getUsername(), loginBody.getPassword());
        return AjaxResult.success(msg);
    }

    /**
     * 发送短信验证码
     *
     * @param loginBody 包含手机号的请求体
     * @return 结果
     */
    @Anonymous
    @ApiOperation("发送短信验证码")
    @PostMapping("/sendSmsCode")
    public AjaxResult sendSmsCode(@RequestBody LoginBody loginBody)
    {
        String msg = appLoginService.sendSmsCode(loginBody.getPhonenumber());
        return AjaxResult.success(msg);
    }
}
