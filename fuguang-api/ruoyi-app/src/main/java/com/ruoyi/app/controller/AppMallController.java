package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.domain.MallCategory;
import com.ruoyi.fuguang.domain.MallProduct;
import com.ruoyi.fuguang.service.IMallCategoryService;
import com.ruoyi.fuguang.service.IMallProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP商城接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP商城接口")
@RestController("appMallController")
@RequestMapping("/app/mall")
public class AppMallController extends BaseController
{
    @Autowired
    private IMallCategoryService mallCategoryService;

    @Autowired
    private IMallProductService mallProductService;

    /**
     * 获取商城首页数据
     */
    @Anonymous
    @ApiOperation("获取商城首页数据")
    @GetMapping("/home")
    public AjaxResult getHomeData()
    {
        Map<String, Object> data = new HashMap<>();
        
        // 获取一级分类列表
        List<MallCategory> categories = mallCategoryService.selectTopCategoryList();
        data.put("categories", categories);
        
        // 获取热门商品（前8个）
        List<MallProduct> hotProducts = mallProductService.selectHotProductList(8);
        data.put("hotProducts", hotProducts);
        
        // 获取新品推荐（前8个）
        List<MallProduct> newProducts = mallProductService.selectNewProductList(8);
        data.put("newProducts", newProducts);
        
        // 获取推荐商品（前8个）
        List<MallProduct> recommendProducts = mallProductService.selectRecommendProductList(8);
        data.put("recommendProducts", recommendProducts);
        
        return success(data);
    }

    /**
     * 获取商品分类列表
     */
    @Anonymous
    @ApiOperation("获取商品分类列表")
    @GetMapping("/categories")
    public AjaxResult getCategoryList()
    {
        List<MallCategory> list = mallCategoryService.selectEnabledCategoryTree();
        return success(list);
    }

    /**
     * 获取商品列表
     */
    @Anonymous
    @ApiOperation("获取商品列表")
    @GetMapping("/products")
    public TableDataInfo getProductList(
            @ApiParam("分类ID") @RequestParam(required = false) Long categoryId,
            @ApiParam("关键词") @RequestParam(required = false) String keyword,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize)
    {
        startPage();
        List<MallProduct> list;
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            // 搜索商品
            list = mallProductService.searchProducts(keyword.trim(), null);
        } else if (categoryId != null) {
            // 按分类查询
            list = mallProductService.selectProductListByCategoryId(categoryId, null);
        } else {
            // 查询所有商品
            MallProduct mallProduct = new MallProduct();
            mallProduct.setProductStatus("0"); // 只查询上架商品
            list = mallProductService.selectMallProductList(mallProduct);
        }
        
        return getDataTable(list);
    }

    /**
     * 获取商品详情
     */
    @Anonymous
    @ApiOperation("获取商品详情")
    @GetMapping("/product/{productId}")
    public AjaxResult getProductDetail(@ApiParam("商品ID") @PathVariable Long productId)
    {
        MallProduct product = mallProductService.selectMallProductByProductId(productId);
        if (product == null) {
            return error("商品不存在");
        }
        
        if (!"0".equals(product.getProductStatus())) {
            return error("商品已下架");
        }
        
        return success(product);
    }

    /**
     * 搜索商品
     */
    @Anonymous
    @ApiOperation("搜索商品")
    @GetMapping("/search")
    public TableDataInfo searchProducts(
            @ApiParam("关键词") @RequestParam String keyword,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize)
    {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getDataTable(null);
        }
        
        startPage();
        List<MallProduct> list = mallProductService.searchProducts(keyword.trim(), null);
        return getDataTable(list);
    }

    /**
     * 获取热门商品
     */
    @Anonymous
    @ApiOperation("获取热门商品")
    @GetMapping("/hot")
    public AjaxResult getHotProducts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<MallProduct> list = mallProductService.selectHotProductList(limit);
        return success(list);
    }

    /**
     * 获取新品推荐
     */
    @Anonymous
    @ApiOperation("获取新品推荐")
    @GetMapping("/new")
    public AjaxResult getNewProducts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<MallProduct> list = mallProductService.selectNewProductList(limit);
        return success(list);
    }

    /**
     * 获取推荐商品
     */
    @Anonymous
    @ApiOperation("获取推荐商品")
    @GetMapping("/recommend")
    public AjaxResult getRecommendProducts(@ApiParam("数量限制") @RequestParam(defaultValue = "10") Integer limit)
    {
        List<MallProduct> list = mallProductService.selectRecommendProductList(limit);
        return success(list);
    }
}
