package com.ruoyi.app.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.domain.MallCart;
import com.ruoyi.fuguang.service.IMallOrderService;
import com.ruoyi.fuguang.service.IMallCartService;
import com.ruoyi.fuguang.service.IMallPaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP订单接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP订单接口")
@RestController("appOrderController")
@RequestMapping("/app/order")
public class AppOrderController extends BaseController
{
    @Autowired
    private IMallOrderService mallOrderService;

    @Autowired
    private IMallCartService mallCartService;

    @Autowired
    private IMallPaymentService mallPaymentService;

    /**
     * 获取订单列表
     */
    @ApiOperation("获取订单列表")
    @GetMapping("/list")
    public TableDataInfo getOrderList(
            @ApiParam("订单状态") @RequestParam(required = false) String orderStatus,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize)
    {
        Long userId = getUserId();
        startPage();
        List<MallOrder> list = mallOrderService.selectOrderListByUserId(userId, orderStatus);
        return getDataTable(list);
    }

    /**
     * 获取订单详情
     */
    @ApiOperation("获取订单详情")
    @GetMapping("/{orderId}")
    public AjaxResult getOrderDetail(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }
        
        return success(order);
    }

    /**
     * 创建订单
     */
    @ApiOperation("创建订单")
    @PostMapping("/create")
    public AjaxResult createOrder(@RequestBody Map<String, Object> params)
    {
        Long userId = getUserId();
        
        // 获取参数
        Long[] cartIds = (Long[]) params.get("cartIds");
        String receiverName = (String) params.get("receiverName");
        String receiverPhone = (String) params.get("receiverPhone");
        String receiverAddress = (String) params.get("receiverAddress");
        String remark = (String) params.get("remark");
        
        if (cartIds == null || cartIds.length == 0) {
            return error("请选择要购买的商品");
        }
        
        if (receiverName == null || receiverName.trim().isEmpty()) {
            return error("请填写收货人姓名");
        }
        
        if (receiverPhone == null || receiverPhone.trim().isEmpty()) {
            return error("请填写收货人电话");
        }
        
        if (receiverAddress == null || receiverAddress.trim().isEmpty()) {
            return error("请填写收货地址");
        }
        
        // 获取购物车商品
        List<MallCart> cartItems = mallCartService.selectCartListByUserId(userId);
        cartItems.removeIf(cart -> {
            for (Long cartId : cartIds) {
                if (cart.getCartId().equals(cartId)) {
                    return false;
                }
            }
            return true;
        });
        
        if (cartItems.isEmpty()) {
            return error("购物车商品不存在");
        }
        
        try {
            MallOrder order = mallOrderService.createOrder(userId, cartItems, 
                receiverName, receiverPhone, receiverAddress, remark);
            
            if (order != null) {
                // 删除购物车中的商品
                mallCartService.removeCartItems(userId, cartIds);
                return success("订单创建成功", order);
            } else {
                return error("订单创建失败");
            }
        } catch (Exception e) {
            return error("订单创建失败：" + e.getMessage());
        }
    }

    /**
     * 取消订单
     */
    @ApiOperation("取消订单")
    @PutMapping("/{orderId}/cancel")
    public AjaxResult cancelOrder(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        int result = mallOrderService.cancelOrder(orderId, userId);
        
        if (result > 0) {
            return success("订单取消成功");
        } else {
            return error("订单取消失败");
        }
    }

    /**
     * 确认收货
     */
    @ApiOperation("确认收货")
    @PutMapping("/{orderId}/confirm")
    public AjaxResult confirmReceive(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        int result = mallOrderService.confirmReceive(orderId, userId);
        
        if (result > 0) {
            return success("确认收货成功");
        } else {
            return error("确认收货失败");
        }
    }

    /**
     * 创建支付订单
     */
    @ApiOperation("创建支付订单")
    @PostMapping("/{orderId}/pay")
    public AjaxResult createPayment(
            @ApiParam("订单ID") @PathVariable Long orderId,
            @ApiParam("支付方式") @RequestParam(defaultValue = "1") String payType)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }
        
        if (!"0".equals(order.getOrderStatus())) {
            return error("订单状态不正确");
        }
        
        try {
            if ("1".equals(payType)) {
                // 支付宝支付
                Map<String, Object> payData = mallPaymentService.createAlipayOrder(order);
                return success("支付订单创建成功", payData);
            } else {
                return error("暂不支持该支付方式");
            }
        } catch (Exception e) {
            return error("支付订单创建失败：" + e.getMessage());
        }
    }

    /**
     * 查询支付状态
     */
    @ApiOperation("查询支付状态")
    @GetMapping("/{orderId}/pay/status")
    public AjaxResult getPaymentStatus(@ApiParam("订单ID") @PathVariable Long orderId)
    {
        Long userId = getUserId();
        MallOrder order = mallOrderService.selectMallOrderByOrderId(orderId);
        
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!order.getUserId().equals(userId)) {
            return error("无权访问此订单");
        }
        
        String payStatus = mallPaymentService.getPaymentStatus(order.getOrderNo());
        
        Map<String, Object> data = new HashMap<>();
        data.put("payStatus", payStatus);
        data.put("orderStatus", order.getOrderStatus());
        
        return success(data);
    }

    /**
     * 获取用户订单统计
     */
    @ApiOperation("获取用户订单统计")
    @GetMapping("/stats")
    public AjaxResult getUserOrderStats()
    {
        Long userId = getUserId();
        Object stats = mallOrderService.getUserOrderStats(userId);
        return success(stats);
    }
}
