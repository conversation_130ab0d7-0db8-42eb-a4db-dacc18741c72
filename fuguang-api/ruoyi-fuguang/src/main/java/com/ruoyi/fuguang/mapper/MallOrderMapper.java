package com.ruoyi.fuguang.mapper;

import java.util.List;
import com.ruoyi.fuguang.domain.MallOrder;
import org.apache.ibatis.annotations.Param;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface MallOrderMapper 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public MallOrder selectMallOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单
     */
    public MallOrder selectMallOrderByOrderNo(String orderNo);

    /**
     * 查询订单列表
     * 
     * @param mallOrder 订单
     * @return 订单集合
     */
    public List<MallOrder> selectMallOrderList(MallOrder mallOrder);

    /**
     * 新增订单
     * 
     * @param mallOrder 订单
     * @return 结果
     */
    public int insertMallOrder(MallOrder mallOrder);

    /**
     * 修改订单
     * 
     * @param mallOrder 订单
     * @return 结果
     */
    public int updateMallOrder(MallOrder mallOrder);

    /**
     * 删除订单
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteMallOrderByOrderId(Long orderId);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMallOrderByOrderIds(Long[] orderIds);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @param orderStatus 订单状态（可选）
     * @return 订单列表
     */
    public List<MallOrder> selectOrderListByUserId(@Param("userId") Long userId, @Param("orderStatus") String orderStatus);

    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param orderStatus 订单状态
     * @return 结果
     */
    public int updateOrderStatus(@Param("orderId") Long orderId, @Param("orderStatus") String orderStatus);

    /**
     * 更新支付状态
     * 
     * @param orderId 订单ID
     * @param payStatus 支付状态
     * @return 结果
     */
    public int updatePayStatus(@Param("orderId") Long orderId, @Param("payStatus") String payStatus);

    /**
     * 根据订单号更新支付状态
     * 
     * @param orderNo 订单号
     * @param payStatus 支付状态
     * @return 结果
     */
    public int updatePayStatusByOrderNo(@Param("orderNo") String orderNo, @Param("payStatus") String payStatus);

    /**
     * 查询待支付订单数量
     * 
     * @param userId 用户ID
     * @return 数量
     */
    public int selectPendingPaymentCount(Long userId);

    /**
     * 查询待发货订单数量
     * 
     * @param userId 用户ID
     * @return 数量
     */
    public int selectPendingDeliveryCount(Long userId);

    /**
     * 查询待收货订单数量
     * 
     * @param userId 用户ID
     * @return 数量
     */
    public int selectPendingReceiveCount(Long userId);
}
