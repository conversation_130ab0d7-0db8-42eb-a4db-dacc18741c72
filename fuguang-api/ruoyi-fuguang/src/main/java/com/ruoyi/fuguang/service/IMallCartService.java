package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.MallCart;

/**
 * 购物车Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IMallCartService 
{
    /**
     * 查询购物车
     * 
     * @param cartId 购物车主键
     * @return 购物车
     */
    public MallCart selectMallCartByCartId(Long cartId);

    /**
     * 查询购物车列表
     * 
     * @param mallCart 购物车
     * @return 购物车集合
     */
    public List<MallCart> selectMallCartList(MallCart mallCart);

    /**
     * 新增购物车
     * 
     * @param mallCart 购物车
     * @return 结果
     */
    public int insertMallCart(MallCart mallCart);

    /**
     * 修改购物车
     * 
     * @param mallCart 购物车
     * @return 结果
     */
    public int updateMallCart(MallCart mallCart);

    /**
     * 批量删除购物车
     * 
     * @param cartIds 需要删除的购物车主键集合
     * @return 结果
     */
    public int deleteMallCartByCartIds(Long[] cartIds);

    /**
     * 删除购物车信息
     * 
     * @param cartId 购物车主键
     * @return 结果
     */
    public int deleteMallCartByCartId(Long cartId);

    /**
     * 根据用户ID查询购物车列表
     * 
     * @param userId 用户ID
     * @return 购物车列表
     */
    public List<MallCart> selectCartListByUserId(Long userId);

    /**
     * 添加商品到购物车
     * 
     * @param userId 用户ID
     * @param productId 商品ID
     * @param quantity 数量
     * @return 结果
     */
    public int addToCart(Long userId, Long productId, Integer quantity);

    /**
     * 更新购物车商品数量
     * 
     * @param userId 用户ID
     * @param productId 商品ID
     * @param quantity 数量
     * @return 结果
     */
    public int updateCartQuantity(Long userId, Long productId, Integer quantity);

    /**
     * 删除购物车中的商品
     * 
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 结果
     */
    public int removeFromCart(Long userId, Long productId);

    /**
     * 清空用户购物车
     * 
     * @param userId 用户ID
     * @return 结果
     */
    public int clearCart(Long userId);

    /**
     * 根据购物车ID列表删除
     * 
     * @param userId 用户ID
     * @param cartIds 购物车ID列表
     * @return 结果
     */
    public int removeCartItems(Long userId, Long[] cartIds);

    /**
     * 获取购物车商品数量
     * 
     * @param userId 用户ID
     * @return 商品数量
     */
    public int getCartItemCount(Long userId);
}
