package com.ruoyi.fuguang.service;

import java.util.List;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.domain.MallCart;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface IMallOrderService 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public MallOrder selectMallOrderByOrderId(Long orderId);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单
     */
    public MallOrder selectMallOrderByOrderNo(String orderNo);

    /**
     * 查询订单列表
     * 
     * @param mallOrder 订单
     * @return 订单集合
     */
    public List<MallOrder> selectMallOrderList(MallOrder mallOrder);

    /**
     * 新增订单
     * 
     * @param mallOrder 订单
     * @return 结果
     */
    public int insertMallOrder(MallOrder mallOrder);

    /**
     * 修改订单
     * 
     * @param mallOrder 订单
     * @return 结果
     */
    public int updateMallOrder(MallOrder mallOrder);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteMallOrderByOrderIds(Long[] orderIds);

    /**
     * 删除订单信息
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteMallOrderByOrderId(Long orderId);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @param orderStatus 订单状态（可选）
     * @return 订单列表
     */
    public List<MallOrder> selectOrderListByUserId(Long userId, String orderStatus);

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param cartItems 购物车商品列表
     * @param receiverName 收货人姓名
     * @param receiverPhone 收货人电话
     * @param receiverAddress 收货地址
     * @param remark 订单备注
     * @return 订单信息
     */
    public MallOrder createOrder(Long userId, List<MallCart> cartItems, String receiverName, String receiverPhone, String receiverAddress, String remark);

    /**
     * 取消订单
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 结果
     */
    public int cancelOrder(Long orderId, Long userId);

    /**
     * 确认收货
     * 
     * @param orderId 订单ID
     * @param userId 用户ID
     * @return 结果
     */
    public int confirmReceive(Long orderId, Long userId);

    /**
     * 发货
     * 
     * @param orderId 订单ID
     * @return 结果
     */
    public int deliverOrder(Long orderId);

    /**
     * 支付成功处理
     * 
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @return 结果
     */
    public int paymentSuccess(String orderNo, String tradeNo);

    /**
     * 支付失败处理
     * 
     * @param orderNo 订单号
     * @return 结果
     */
    public int paymentFailed(String orderNo);

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    public String generateOrderNo();

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 订单统计信息
     */
    public Object getUserOrderStats(Long userId);
}
