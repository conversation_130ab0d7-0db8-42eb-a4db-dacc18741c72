package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.MallCartMapper;
import com.ruoyi.fuguang.mapper.MallProductMapper;
import com.ruoyi.fuguang.domain.MallCart;
import com.ruoyi.fuguang.domain.MallProduct;
import com.ruoyi.fuguang.service.IMallCartService;

/**
 * 购物车Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MallCartServiceImpl implements IMallCartService
{
    @Autowired
    private MallCartMapper mallCartMapper;

    @Autowired
    private MallProductMapper mallProductMapper;

    /**
     * 查询购物车
     *
     * @param cartId 购物车主键
     * @return 购物车
     */
    @Override
    public MallCart selectMallCartByCartId(Long cartId)
    {
        return mallCartMapper.selectMallCartByCartId(cartId);
    }

    /**
     * 查询购物车列表
     *
     * @param mallCart 购物车
     * @return 购物车
     */
    @Override
    public List<MallCart> selectMallCartList(MallCart mallCart)
    {
        return mallCartMapper.selectMallCartList(mallCart);
    }

    /**
     * 新增购物车
     *
     * @param mallCart 购物车
     * @return 结果
     */
    @Override
    public int insertMallCart(MallCart mallCart)
    {
        mallCart.setCreateTime(DateUtils.getNowDate());
        return mallCartMapper.insertMallCart(mallCart);
    }

    /**
     * 修改购物车
     *
     * @param mallCart 购物车
     * @return 结果
     */
    @Override
    public int updateMallCart(MallCart mallCart)
    {
        mallCart.setUpdateTime(DateUtils.getNowDate());
        return mallCartMapper.updateMallCart(mallCart);
    }

    /**
     * 批量删除购物车
     *
     * @param cartIds 需要删除的购物车主键
     * @return 结果
     */
    @Override
    public int deleteMallCartByCartIds(Long[] cartIds)
    {
        return mallCartMapper.deleteMallCartByCartIds(cartIds);
    }

    /**
     * 删除购物车信息
     *
     * @param cartId 购物车主键
     * @return 结果
     */
    @Override
    public int deleteMallCartByCartId(Long cartId)
    {
        return mallCartMapper.deleteMallCartByCartId(cartId);
    }

    /**
     * 根据用户ID查询购物车列表
     *
     * @param userId 用户ID
     * @return 购物车列表
     */
    @Override
    public List<MallCart> selectCartListByUserId(Long userId)
    {
        return mallCartMapper.selectCartListByUserId(userId);
    }

    /**
     * 添加商品到购物车
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @param quantity 数量
     * @return 结果
     */
    @Override
    @Transactional
    public int addToCart(Long userId, Long productId, Integer quantity)
    {
        // 检查商品是否存在且上架
        MallProduct product = mallProductMapper.selectMallProductByProductId(productId);
        if (product == null || !"0".equals(product.getProductStatus())) {
            throw new RuntimeException("商品不存在或已下架");
        }

        // 检查库存
        if (product.getStockQuantity() < quantity) {
            throw new RuntimeException("商品库存不足");
        }

        // 检查购物车中是否已存在该商品
        MallCart existCart = mallCartMapper.selectCartByUserIdAndProductId(userId, productId);
        if (existCart != null) {
            // 更新数量
            int newQuantity = existCart.getQuantity() + quantity;
            if (product.getStockQuantity() < newQuantity) {
                throw new RuntimeException("商品库存不足");
            }
            return mallCartMapper.updateCartQuantity(userId, productId, newQuantity);
        } else {
            // 新增购物车记录
            MallCart cart = new MallCart();
            cart.setUserId(userId);
            cart.setProductId(productId);
            cart.setQuantity(quantity);
            cart.setCreateTime(new Date());
            cart.setUpdateTime(new Date());
            return mallCartMapper.insertMallCart(cart);
        }
    }

    /**
     * 更新购物车商品数量
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @param quantity 数量
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCartQuantity(Long userId, Long productId, Integer quantity)
    {
        // 检查商品库存
        MallProduct product = mallProductMapper.selectMallProductByProductId(productId);
        if (product == null || !"0".equals(product.getProductStatus())) {
            throw new RuntimeException("商品不存在或已下架");
        }

        if (product.getStockQuantity() < quantity) {
            throw new RuntimeException("商品库存不足");
        }

        return mallCartMapper.updateCartQuantity(userId, productId, quantity);
    }

    /**
     * 删除购物车中的商品
     *
     * @param userId 用户ID
     * @param productId 商品ID
     * @return 结果
     */
    @Override
    public int removeFromCart(Long userId, Long productId)
    {
        return mallCartMapper.deleteCartByUserIdAndProductId(userId, productId);
    }

    /**
     * 清空用户购物车
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int clearCart(Long userId)
    {
        return mallCartMapper.clearCartByUserId(userId);
    }

    /**
     * 获取购物车商品数量
     *
     * @param userId 用户ID
     * @return 商品数量
     */
    @Override
    public int getCartItemCount(Long userId)
    {
        List<MallCart> cartList = mallCartMapper.selectCartListByUserId(userId);
        int totalCount = 0;
        for (MallCart cart : cartList) {
            totalCount += cart.getQuantity();
        }
        return totalCount;
    }

    /**
     * 批量删除购物车商品
     *
     * @param userId 用户ID
     * @param cartIds 购物车ID数组
     * @return 结果
     */
    @Override
    public int removeCartItems(Long userId, Long[] cartIds)
    {
        return mallCartMapper.deleteCartByUserIdAndCartIds(userId, cartIds);
    }
}