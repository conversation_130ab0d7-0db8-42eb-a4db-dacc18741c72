package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.config.AlipayConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.fuguang.mapper.MallPaymentMapper;
import com.ruoyi.fuguang.domain.MallPayment;
import com.ruoyi.fuguang.domain.MallOrder;
import com.ruoyi.fuguang.service.IMallPaymentService;
import com.ruoyi.fuguang.service.IMallOrderService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 支付记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class MallPaymentServiceImpl implements IMallPaymentService 
{
    private static final Logger log = LoggerFactory.getLogger(MallPaymentServiceImpl.class);

    @Autowired
    private MallPaymentMapper mallPaymentMapper;

    @Autowired
    private IMallOrderService mallOrderService;

    @Autowired
    private AlipayConfig alipayConfig;

    /**
     * 查询支付记录
     * 
     * @param paymentId 支付记录主键
     * @return 支付记录
     */
    @Override
    public MallPayment selectMallPaymentByPaymentId(Long paymentId)
    {
        return mallPaymentMapper.selectMallPaymentByPaymentId(paymentId);
    }

    /**
     * 查询支付记录列表
     * 
     * @param mallPayment 支付记录
     * @return 支付记录
     */
    @Override
    public List<MallPayment> selectMallPaymentList(MallPayment mallPayment)
    {
        return mallPaymentMapper.selectMallPaymentList(mallPayment);
    }

    /**
     * 新增支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    @Override
    public int insertMallPayment(MallPayment mallPayment)
    {
        mallPayment.setCreateTime(DateUtils.getNowDate());
        return mallPaymentMapper.insertMallPayment(mallPayment);
    }

    /**
     * 修改支付记录
     * 
     * @param mallPayment 支付记录
     * @return 结果
     */
    @Override
    public int updateMallPayment(MallPayment mallPayment)
    {
        mallPayment.setUpdateTime(DateUtils.getNowDate());
        return mallPaymentMapper.updateMallPayment(mallPayment);
    }

    /**
     * 批量删除支付记录
     * 
     * @param paymentIds 需要删除的支付记录主键
     * @return 结果
     */
    @Override
    public int deleteMallPaymentByPaymentIds(Long[] paymentIds)
    {
        return mallPaymentMapper.deleteMallPaymentByPaymentIds(paymentIds);
    }

    /**
     * 删除支付记录信息
     * 
     * @param paymentId 支付记录主键
     * @return 结果
     */
    @Override
    public int deleteMallPaymentByPaymentId(Long paymentId)
    {
        return mallPaymentMapper.deleteMallPaymentByPaymentId(paymentId);
    }

    /**
     * 根据订单ID查询支付记录
     * 
     * @param orderId 订单ID
     * @return 支付记录
     */
    @Override
    public MallPayment selectPaymentByOrderId(Long orderId)
    {
        return mallPaymentMapper.selectPaymentByOrderId(orderId);
    }

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 支付记录
     */
    @Override
    public MallPayment selectPaymentByOrderNo(String orderNo)
    {
        return mallPaymentMapper.selectPaymentByOrderNo(orderNo);
    }

    /**
     * 创建支付记录
     * 
     * @param order 订单信息
     * @param payType 支付方式
     * @return 支付记录
     */
    @Override
    public MallPayment createPayment(MallOrder order, String payType)
    {
        MallPayment payment = new MallPayment();
        payment.setOrderId(order.getOrderId());
        payment.setOrderNo(order.getOrderNo());
        payment.setUserId(order.getUserId());
        payment.setPayAmount(order.getPayAmount());
        payment.setPayType(payType);
        payment.setPayStatus("0"); // 待支付
        payment.setCreateTime(new Date());
        
        mallPaymentMapper.insertMallPayment(payment);
        return payment;
    }

    /**
     * 创建支付宝支付订单
     * 
     * @param order 订单信息
     * @return 支付参数
     */
    @Override
    public Map<String, Object> createAlipayOrder(MallOrder order)
    {
        try {
            // 创建支付记录
            MallPayment payment = createPayment(order, "1");
            
            // 创建支付宝客户端
            AlipayClient alipayClient = new DefaultAlipayClient(
                alipayConfig.getGatewayUrl(),
                alipayConfig.getAppId(),
                alipayConfig.getPrivateKey(),
                "json",
                alipayConfig.getCharset(),
                alipayConfig.getPublicKey(),
                alipayConfig.getSignType()
            );
            
            // 创建API对应的request
            AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
            
            // 设置异步通知地址
            request.setNotifyUrl(alipayConfig.getNotifyUrl());
            
            // 设置请求参数
            Map<String, Object> bizContent = new HashMap<>();
            bizContent.put("out_trade_no", order.getOrderNo());
            bizContent.put("total_amount", order.getPayAmount().toString());
            bizContent.put("subject", "商城订单-" + order.getOrderNo());
            bizContent.put("product_code", "QUICK_MSECURITY_PAY");
            
            ObjectMapper objectMapper = new ObjectMapper();
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            
            // 调用SDK生成表单
            AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
            
            if (response.isSuccess()) {
                Map<String, Object> result = new HashMap<>();
                result.put("orderString", response.getBody());
                result.put("orderNo", order.getOrderNo());
                return result;
            } else {
                log.error("支付宝支付订单创建失败：{}", response.getSubMsg());
                throw new RuntimeException("支付宝支付订单创建失败：" + response.getSubMsg());
            }
        } catch (Exception e) {
            log.error("创建支付宝支付订单异常", e);
            throw new RuntimeException("创建支付宝支付订单异常：" + e.getMessage());
        }
    }

    /**
     * 处理支付宝支付回调
     * 
     * @param params 回调参数
     * @return 处理结果
     */
    @Override
    public boolean handleAlipayCallback(Map<String, String> params)
    {
        try {
            // 验证签名
            boolean signVerified = AlipaySignature.rsaCheckV1(
                params, 
                alipayConfig.getPublicKey(), 
                alipayConfig.getCharset(), 
                alipayConfig.getSignType()
            );
            
            if (!signVerified) {
                log.error("支付宝回调签名验证失败");
                return false;
            }
            
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                // 支付成功
                return paymentSuccess(outTradeNo, tradeNo, "1") > 0;
            } else {
                log.warn("支付宝回调状态异常：{}", tradeStatus);
                return false;
            }
        } catch (Exception e) {
            log.error("处理支付宝回调异常", e);
            return false;
        }
    }

    /**
     * 支付成功处理
     * 
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @param payType 支付方式
     * @return 结果
     */
    @Override
    public int paymentSuccess(String orderNo, String tradeNo, String payType)
    {
        try {
            // 更新支付记录
            mallPaymentMapper.updatePaymentStatusByOrderNo(orderNo, "1", tradeNo);
            
            // 更新订单状态
            mallOrderService.paymentSuccess(orderNo, tradeNo);
            
            return 1;
        } catch (Exception e) {
            log.error("支付成功处理异常", e);
            return 0;
        }
    }

    /**
     * 支付失败处理
     * 
     * @param orderNo 订单号
     * @return 结果
     */
    @Override
    public int paymentFailed(String orderNo)
    {
        try {
            // 更新支付记录
            mallPaymentMapper.updatePaymentStatusByOrderNo(orderNo, "2", null);
            
            // 更新订单状态
            mallOrderService.paymentFailed(orderNo);
            
            return 1;
        } catch (Exception e) {
            log.error("支付失败处理异常", e);
            return 0;
        }
    }

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态
     */
    @Override
    public String getPaymentStatus(String orderNo)
    {
        MallPayment payment = mallPaymentMapper.selectPaymentByOrderNo(orderNo);
        return payment != null ? payment.getPayStatus() : "0";
    }
}
