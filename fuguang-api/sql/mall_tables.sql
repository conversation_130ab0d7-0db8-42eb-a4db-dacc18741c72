-- ----------------------------
-- 商城相关数据库表结构
-- ----------------------------

-- ----------------------------
-- 1、商品分类表
-- ----------------------------
DROP TABLE IF EXISTS `mall_category`;
CREATE TABLE `mall_category` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父分类ID',
  `category_name` varchar(50) NOT NULL COMMENT '分类名称',
  `category_icon` varchar(200) DEFAULT '' COMMENT '分类图标',
  `sort_order` int(4) DEFAULT 0 COMMENT '显示顺序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商品分类表';

-- ----------------------------
-- 2、商品信息表
-- ----------------------------
DROP TABLE IF EXISTS `mall_product`;
CREATE TABLE `mall_product` (
  `product_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_desc` text COMMENT '商品描述',
  `product_image` varchar(500) DEFAULT '' COMMENT '商品主图',
  `product_images` text COMMENT '商品图片集合（JSON格式）',
  `original_price` decimal(10,2) DEFAULT 0.00 COMMENT '原价',
  `sale_price` decimal(10,2) NOT NULL COMMENT '销售价格',
  `stock_quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `sales_count` int(11) DEFAULT 0 COMMENT '销量',
  `product_status` char(1) DEFAULT '0' COMMENT '商品状态（0上架 1下架）',
  `is_hot` char(1) DEFAULT '0' COMMENT '是否热门（0否 1是）',
  `is_new` char(1) DEFAULT '0' COMMENT '是否新品（0否 1是）',
  `is_recommend` char(1) DEFAULT '0' COMMENT '是否推荐（0否 1是）',
  `sort_order` int(4) DEFAULT 0 COMMENT '显示顺序',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`product_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_product_status` (`product_status`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_is_new` (`is_new`),
  KEY `idx_is_recommend` (`is_recommend`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='商品信息表';

-- ----------------------------
-- 3、购物车表
-- ----------------------------
DROP TABLE IF EXISTS `mall_cart`;
CREATE TABLE `mall_cart` (
  `cart_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '商品数量',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`cart_id`),
  UNIQUE KEY `uk_user_product` (`user_id`,`product_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='购物车表';

-- ----------------------------
-- 4、订单表
-- ----------------------------
DROP TABLE IF EXISTS `mall_order`;
CREATE TABLE `mall_order` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `order_status` char(1) DEFAULT '0' COMMENT '订单状态（0待付款 1已付款 2已发货 3已完成 4已取消 5已退款）',
  `pay_status` char(1) DEFAULT '0' COMMENT '支付状态（0未支付 1已支付 2支付失败）',
  `pay_type` char(1) DEFAULT '1' COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `delivery_time` datetime DEFAULT NULL COMMENT '发货时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `receiver_name` varchar(50) DEFAULT '' COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT '' COMMENT '收货人电话',
  `receiver_address` varchar(200) DEFAULT '' COMMENT '收货地址',
  `delivery_fee` decimal(10,2) DEFAULT 0.00 COMMENT '配送费',
  `remark` varchar(500) DEFAULT NULL COMMENT '订单备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='订单表';

-- ----------------------------
-- 5、订单详情表
-- ----------------------------
DROP TABLE IF EXISTS `mall_order_item`;
CREATE TABLE `mall_order_item` (
  `item_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单项ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `product_id` bigint(20) NOT NULL COMMENT '商品ID',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_image` varchar(200) DEFAULT '' COMMENT '商品图片',
  `product_price` decimal(10,2) NOT NULL COMMENT '商品价格',
  `quantity` int(11) NOT NULL COMMENT '购买数量',
  `total_price` decimal(10,2) NOT NULL COMMENT '小计金额',
  PRIMARY KEY (`item_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='订单详情表';

-- ----------------------------
-- 6、支付记录表
-- ----------------------------
DROP TABLE IF EXISTS `mall_payment`;
CREATE TABLE `mall_payment` (
  `payment_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付记录ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `pay_amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `pay_type` char(1) NOT NULL COMMENT '支付方式（1支付宝 2微信 3余额）',
  `pay_status` char(1) DEFAULT '0' COMMENT '支付状态（0待支付 1支付成功 2支付失败 3已退款）',
  `trade_no` varchar(64) DEFAULT '' COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `notify_time` datetime DEFAULT NULL COMMENT '通知时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`payment_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_trade_no` (`trade_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1000 COMMENT='支付记录表';

-- ----------------------------
-- 初始化商品分类数据
-- ----------------------------
INSERT INTO `mall_category` VALUES (1, 0, '数码电器', '', 1, '0', '0', 'admin', NOW(), '', NULL, '数码电器分类');
INSERT INTO `mall_category` VALUES (2, 1, '手机通讯', '', 1, '0', '0', 'admin', NOW(), '', NULL, '手机通讯分类');
INSERT INTO `mall_category` VALUES (3, 1, '电脑办公', '', 2, '0', '0', 'admin', NOW(), '', NULL, '电脑办公分类');
INSERT INTO `mall_category` VALUES (4, 0, '服装鞋帽', '', 2, '0', '0', 'admin', NOW(), '', NULL, '服装鞋帽分类');
INSERT INTO `mall_category` VALUES (5, 4, '男装', '', 1, '0', '0', 'admin', NOW(), '', NULL, '男装分类');
INSERT INTO `mall_category` VALUES (6, 4, '女装', '', 2, '0', '0', 'admin', NOW(), '', NULL, '女装分类');
INSERT INTO `mall_category` VALUES (7, 0, '家居生活', '', 3, '0', '0', 'admin', NOW(), '', NULL, '家居生活分类');
INSERT INTO `mall_category` VALUES (8, 7, '家具', '', 1, '0', '0', 'admin', NOW(), '', NULL, '家具分类');
INSERT INTO `mall_category` VALUES (9, 7, '家电', '', 2, '0', '0', 'admin', NOW(), '', NULL, '家电分类');

-- ----------------------------
-- 商城管理菜单权限
-- ----------------------------
-- 商城管理主菜单
INSERT INTO sys_menu VALUES('3000', '商城管理', '0', '6', 'mall', NULL, '', '', 1, 0, 'M', '0', '0', '', 'shopping', 'admin', NOW(), '', NULL, '商城管理目录');

-- 商品分类管理菜单
INSERT INTO sys_menu VALUES('3001', '商品分类', '3000', '1', 'category', 'mall/category/index', '', '', 1, 0, 'C', '0', '0', 'mall:category:list', 'tree', 'admin', NOW(), '', NULL, '商品分类管理菜单');
INSERT INTO sys_menu VALUES('3002', '分类查询', '3001', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3003', '分类新增', '3001', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3004', '分类修改', '3001', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3005', '分类删除', '3001', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:category:remove', '#', 'admin', NOW(), '', NULL, '');

-- 商品管理菜单
INSERT INTO sys_menu VALUES('3010', '商品管理', '3000', '2', 'product', 'mall/product/index', '', '', 1, 0, 'C', '0', '0', 'mall:product:list', 'goods', 'admin', NOW(), '', NULL, '商品管理菜单');
INSERT INTO sys_menu VALUES('3011', '商品查询', '3010', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3012', '商品新增', '3010', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:add', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3013', '商品修改', '3010', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:edit', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3014', '商品删除', '3010', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:remove', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3015', '商品导出', '3010', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:product:export', '#', 'admin', NOW(), '', NULL, '');

-- 订单管理菜单
INSERT INTO sys_menu VALUES('3020', '订单管理', '3000', '3', 'order', 'mall/order/index', '', '', 1, 0, 'C', '0', '0', 'mall:order:list', 'list', 'admin', NOW(), '', NULL, '订单管理菜单');
INSERT INTO sys_menu VALUES('3021', '订单查询', '3020', '1', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:query', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3022', '订单详情', '3020', '2', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:detail', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3023', '订单发货', '3020', '3', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:delivery', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3024', '订单取消', '3020', '4', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:cancel', '#', 'admin', NOW(), '', NULL, '');
INSERT INTO sys_menu VALUES('3025', '订单导出', '3020', '5', '', '', '', '', 1, 0, 'F', '0', '0', 'mall:order:export', '#', 'admin', NOW(), '', NULL, '');

-- 给管理员角色分配商城菜单权限
INSERT INTO sys_role_menu VALUES ('1', '3000');
INSERT INTO sys_role_menu VALUES ('1', '3001');
INSERT INTO sys_role_menu VALUES ('1', '3002');
INSERT INTO sys_role_menu VALUES ('1', '3003');
INSERT INTO sys_role_menu VALUES ('1', '3004');
INSERT INTO sys_role_menu VALUES ('1', '3005');
INSERT INTO sys_role_menu VALUES ('1', '3010');
INSERT INTO sys_role_menu VALUES ('1', '3011');
INSERT INTO sys_role_menu VALUES ('1', '3012');
INSERT INTO sys_role_menu VALUES ('1', '3013');
INSERT INTO sys_role_menu VALUES ('1', '3014');
INSERT INTO sys_role_menu VALUES ('1', '3015');
INSERT INTO sys_role_menu VALUES ('1', '3020');
INSERT INTO sys_role_menu VALUES ('1', '3021');
INSERT INTO sys_role_menu VALUES ('1', '3022');
INSERT INTO sys_role_menu VALUES ('1', '3023');
INSERT INTO sys_role_menu VALUES ('1', '3024');
INSERT INTO sys_role_menu VALUES ('1', '3025');
